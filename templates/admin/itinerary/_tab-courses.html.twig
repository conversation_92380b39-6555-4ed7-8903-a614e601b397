<div class="col-lg-12 d-flex align-items-center justify-content-between py-2 px-0" style="border-bottom: 1px solid var(--color-neutral-light);">
    <span class="mx-1" style="font-weight: 500; color: var(--color-primary-darkest)">
        <i class="fas fa-university"></i>
        ${ courses.length } {{ 'stats.accumulative.courses'|trans({}, 'messages') }}
    </span>
    <button class="btn btn-primary" @click="editCourses()">
        <i class="fa fa-edit"></i>
        {#${ $t('ITINERARY.COURSE.ADD') }#}
        {{ 'itinerary.courses.modify'|trans({}, 'messages') }}
    </button>
</div>
<div class="col-lg-12 " style="border-bottom: 1px solid var(--color-neutral-light);">
    <div v-if="loadingCourses" class="loader-container d-flex align-items-center justify-content-center">
        <loader :is-loaded="loadingCourses"></loader>
    </div>
    <div v-if="courses.length === 0 && !loadingCourses" class="d-flex align-items-center justify-content-center">
        <span>{{ 'itinerary.no_courses'|trans({}, 'messages',  app.user.locale) }}</span>        
    </div>
    <div v-if="courses.length !== 0 && !loadingCourses" class=" justify-content-center">
    {% if not app.user.isTeamManager %}
        <div class="col-md-12 p-0 d-flex mb-2" style="margin-top: 10px">
            <div v-show="appliedCoursesFilters">
                <span class="mx-1" style="font-weight: 500; color: var(--color-primary-darkest)" v-show="true" >
                    <i class="fas fa-filter"></i>
                      ${itineraryFilteredCourses.length} {{ 'itinerary.courses.appliedfilter'|trans({}, 'messages',  app.user.locale) }} ${appliedCategoryCourseFilter}).
                </span>
            </div>
            <div class="ml-auto">
                <button type="button" class="btn btn-default" @click="openCoursesFilter">
                    <i class="fas fa-filter"></i>{{ 'menu.users_managment.filter'|trans({}, 'messages',  app.user.locale) }}
                </button>
            </div>
        </div>
        <div class="col-md-12" style="background: var(--color-neutral-lightest)" v-show="showCoursesFilters">
            {{ include('admin/itinerary/course-filters.html.twig') }}
        </div>
    {% endif %}
    </div>
</div>

<div class="row courses m-0">
   
    <draggable v-if="!loadingCourses" :list="courses" draggable=".card" @end="changedCoursePosition">
        <div class="card course-card drag-el" v-for="(course, i) in itineraryFilteredCourses" :key="course.id" style="cursor:grab">
            <div class="course-image">
                <img alt="" v-bind:src="imagePath(course.image)">
                <span class="position">${ course.position }</span>
            </div>
            <div class="course-body">
                <a :href="'admin/itinerary/redirect-to-course/' + course.id">
                    <span class="course-title" data-toggle="tooltip" data-placement="top" v-bind:title="course.name" >${ course.name }</span>
                </a>
                <span class="course-code" data-toggle="tooltip" data-placement="top" v-bind:title="course.code">${ course.code }</span>
                <span class="course-category">${course.category}</span>
                <span class="delete-action btn btn-sm btn-danger" @click="deleteSelectedCourse(i)"><i class="fa fa-trash"></i></span>
            </div>
        </div>
    </draggable>
</div>
