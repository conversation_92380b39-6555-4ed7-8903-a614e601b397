<?php

declare(strict_types=1);

namespace App\V2\Application\Command;

use App\V2\Domain\Bus\Command;
use App\V2\Domain\Shared\Uuid\Uuid;

readonly class PostLtiDeploymentCommand implements Command
{
    public function __construct(
        private Uuid $registrationId,
        private string $name,
        private string $deploymentId,
    ) {
    }

    public function getRegistrationId(): Uuid
    {
        return $this->registrationId;
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function getDeploymentId(): string
    {
        return $this->deploymentId;
    }
}
