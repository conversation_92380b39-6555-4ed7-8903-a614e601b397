<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\Persistence\Subscription;

use App\V2\Domain\Shared\Collection\CollectionException;
use App\V2\Domain\Shared\Criteria\CriteriaException;
use App\V2\Domain\Shared\Uuid\InvalidUuidException;
use App\V2\Domain\Shared\Uuid\Uuid;
use App\V2\Domain\Subscription\Exception\SubscriptionNotFoundException;
use App\V2\Domain\Subscription\Exception\SubscriptionRepositoryException;
use App\V2\Domain\Subscription\Subscription;
use App\V2\Domain\Subscription\SubscriptionCollection;
use App\V2\Domain\Subscription\SubscriptionCriteria;
use App\V2\Domain\Subscription\SubscriptionRepository;
use App\V2\Infrastructure\Persistence\DBALCommonCriteriaBuilder;
use App\V2\Infrastructure\Persistence\DBALDateTimeFormatter;
use Doctrine\DBAL\Connection;
use Doctrine\DBAL\Exception as DBALException;
use Doctrine\DBAL\Query\QueryBuilder;

readonly class DBALSubscriptionRepository implements SubscriptionRepository
{
    public function __construct(
        private Connection $connection,
        private string $subscriptionTableName,
    ) {
    }

    /**
     * @throws SubscriptionRepositoryException
     * @throws InvalidUuidException
     * @throws CriteriaException
     */
    #[\Override]
    public function put(Subscription $subscription): void
    {
        try {
            $this->findOneBy(SubscriptionCriteria::createById($subscription->getId()));

            $this->update($subscription);
        } catch (SubscriptionNotFoundException) {
            $this->insert($subscription);
        }
    }

    /**
     * @throws SubscriptionRepositoryException
     */
    private function update(Subscription $subscription): void
    {
        try {
            $this->connection->update(
                table: $this->subscriptionTableName,
                data: $this->fromSubscriptionToArray($subscription),
                criteria: ['id' => $subscription->getId()->value()]
            );
        } catch (DBALException $e) {
            throw SubscriptionRepositoryException::fromPrevious($e);
        }
    }

    /**
     * @throws SubscriptionRepositoryException
     */
    private function insert(Subscription $subscription): void
    {
        try {
            $this->connection->insert(
                table: $this->subscriptionTableName,
                data: $this->fromSubscriptionToArray($subscription),
            );
        } catch (DBALException $e) {
            throw SubscriptionRepositoryException::fromPrevious($e);
        }
    }

    /**
     * @throws InvalidUuidException
     * @throws SubscriptionNotFoundException
     * @throws SubscriptionRepositoryException
     */
    #[\Override]
    public function findOneBy(SubscriptionCriteria $criteria): Subscription
    {
        try {
            $result = $this->getQueryBuilderByCriteria($criteria)
                ->executeQuery()
                ->fetchAssociative();

            if (false === $result) {
                throw new SubscriptionNotFoundException();
            }

            return $this->fromArrayToSubscription($result);
        } catch (DBALException $e) {
            throw SubscriptionRepositoryException::fromPrevious($e);
        }
    }

    /**
     * @throws SubscriptionRepositoryException
     * @throws CollectionException
     * @throws InvalidUuidException
     */
    #[\Override]
    public function findBy(SubscriptionCriteria $criteria): SubscriptionCollection
    {
        try {
            $result = $this->getQueryBuilderByCriteria($criteria)
                ->executeQuery()
                ->fetchAllAssociative();

            return new SubscriptionCollection(
                array_map(
                    fn (array $values) => $this->fromArrayToSubscription($values),
                    $result,
                )
            );
        } catch (DBALException $e) {
            throw SubscriptionRepositoryException::fromPrevious($e);
        }
    }

    /**
     * @throws SubscriptionRepositoryException
     */
    #[\Override]
    public function delete(Subscription $subscription): void
    {
        try {
            $subscription->markAsDeleted();

            $this->update($subscription);
        } catch (DBALException $e) {
            throw SubscriptionRepositoryException::fromPrevious($e);
        }
    }

    private function fromSubscriptionToArray(Subscription $subscription): array
    {
        return [
            'id' => $subscription->getId()->value(),
            'name' => $subscription->getName(),
            'description' => $subscription->getDescription(),
            'created_at' => DBALDateTimeFormatter::format($subscription->getCreatedAt()),
            'updated_at' => DBALDateTimeFormatter::format($subscription->getUpdatedAt()),
            'deleted_at' => DBALDateTimeFormatter::format($subscription->getDeletedAt()),
        ];
    }

    /**
     * @throws InvalidUuidException
     */
    private function fromArrayToSubscription(array $data): Subscription
    {
        return new Subscription(
            id: new Uuid($data['id']),
            name: $data['name'],
            description: $data['description'],
            createdAt: DBALDateTimeFormatter::parse($data['created_at']),
            updatedAt: DBALDateTimeFormatter::parse($data['updated_at']),
            deletedAt: DBALDateTimeFormatter::parse($data['deleted_at']),
        );
    }

    private function getQueryBuilderByCriteria(SubscriptionCriteria $criteria): QueryBuilder
    {
        $queryBuilder = $this->connection->createQueryBuilder()
            ->select('t.*')
            ->from($this->subscriptionTableName, 't')
            ->andWhere('t.deleted_at IS NULL');

        if (null !== $criteria->getName()) {
            $queryBuilder->andWhere('t.name = :name')
                ->setParameter('name', $criteria->getName());
        }

        if (null !== $criteria->getSearch()) {
            $queryBuilder->andWhere('(t.name LIKE :search OR t.description LIKE :search)')
                ->setParameter('search', '%' . $criteria->getSearch() . '%');
        }

        DBALCommonCriteriaBuilder::filterByCommonCriteria(
            criteria: $criteria,
            queryBuilder: $queryBuilder,
        );

        return $queryBuilder;
    }
}
