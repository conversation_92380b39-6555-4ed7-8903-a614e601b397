<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\Course\Creator;

use App\V2\Domain\Course\Creator\CourseCreator;
use App\V2\Domain\Course\Creator\CourseCreatorCollection;
use App\V2\Domain\Course\Creator\Creator;

class CourseCreatorTransformer
{
    public static function fromCollectionToArray(CourseCreatorCollection $collection): array
    {
        return array_map(
            fn (CourseCreator $courseCreator) => $courseCreator->getCreator()
                ? self::fromCreatorToArray($courseCreator->getCreator())
                : [
                    'id' => $courseCreator->getUserId()->value(),
                ],
            $collection->all()
        );
    }

    public static function fromCreatorToArray(Creator $creator): array
    {
        return [
            'id' => $creator->getId()->value(),
            'email' => $creator->getEmail()->value(),
            'name' => $creator->getFirstName(),
            'lastName' => $creator->getLastName(),
        ];
    }
}
