<?php

declare(strict_types=1);

namespace App\V2\Domain\Subscription;

use App\V2\Domain\Subscription\Exception\SubscriptionNotFoundException;
use App\V2\Domain\Subscription\Exception\SubscriptionRepositoryException;

interface SubscriptionRepository
{
    /**
     * @throws SubscriptionRepositoryException
     */
    public function put(Subscription $subscription): void;

    /**
     * @throws SubscriptionRepositoryException
     * @throws SubscriptionNotFoundException
     */
    public function findOneBy(SubscriptionCriteria $criteria): Subscription;

    /**
     * @throws SubscriptionRepositoryException
     */
    public function findBy(SubscriptionCriteria $criteria): SubscriptionCollection;

    /**
     * @throws SubscriptionRepositoryException
     */
    public function delete(Subscription $subscription): void;
}
