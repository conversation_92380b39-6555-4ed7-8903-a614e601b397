<?php

declare(strict_types=1);

namespace App\V2\Domain\Course\Creator;

use App\V2\Domain\Shared\Hydrator\HydrationCriteria;

class CourseCreatorHydrationCriteria extends HydrationCriteria
{
    private bool $withUser = false;

    public function isEmpty(): bool
    {
        return !$this->withUser;
    }

    public function withUser(): self
    {
        $this->withUser = true;

        return $this;
    }

    public function needsUser(): bool
    {
        return $this->withUser;
    }
}
