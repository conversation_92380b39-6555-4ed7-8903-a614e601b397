<?php

namespace App\Entity;

use App\Repository\HistorySeenMaterialRepository;
use App\Behavior\Blamable;
use App\Behavior\Timestampable;
use DateTime;
use Doctrine\ORM\Mapping as ORM;

/**
 * @ORM\Entity(repositoryClass=HistorySeenMaterialRepository::class)
 */
class HistorySeenMaterial
{
    /**
     * @ORM\Id
     * @ORM\GeneratedValue
     * @ORM\Column(type="integer")
     */
    private $id;

    /**
     * @ORM\ManyToOne(targetEntity=Course::class, inversedBy="historySeenMaterials")
     */
    private $Course;

    /**
     * @ORM\ManyToOne(targetEntity=User::class, inversedBy="historySeenMaterials")
     */
    private $user;

    /**
     * @ORM\ManyToOne(targetEntity=MaterialCourse::class, inversedBy="historySeenMaterials")
     */
    private $materialCourse;

	public function __construct()
	{
		$this->createdAt = new DateTime();
		$this->updatedAt = new DateTime();
	}

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getCourse(): ?Course
    {
        return $this->Course;
    }

    public function setCourse(?Course $Course): self
    {
        $this->Course = $Course;

        return $this;
    }

    public function getUser(): ?User
    {
        return $this->user;
    }

    public function setUser(?User $user): self
    {
        $this->user = $user;

        return $this;
    }

    public function getMaterialCourse(): ?MaterialCourse
    {
        return $this->materialCourse;
    }

    public function setMaterialCourse(?MaterialCourse $materialCourse): self
    {
        $this->materialCourse = $materialCourse;

        return $this;
    }
}
