<?php

namespace App\Entity;

use App\Repository\ItineraryManagerRepository;
use Doctrine\ORM\Mapping as ORM;

/**
 * @ORM\Entity(repositoryClass=ItineraryManagerRepository::class)
 */
class ItineraryManager
{
    /**
     * @ORM\Id
     * @ORM\GeneratedValue
     * @ORM\Column(type="integer")
     */
    private $id;

    /**
     * @ORM\ManyToOne(targetEntity=Itinerary::class, inversedBy="itineraryManagers")
     */
    private $itinerary;

    /**
     * @ORM\ManyToOne(targetEntity=User::class, inversedBy="itineraryManagers")
     */
    private $user;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getItinerary(): ?Itinerary
    {
        return $this->itinerary;
    }

    public function setItinerary(?Itinerary $itinerary): self
    {
        $this->itinerary = $itinerary;

        return $this;
    }

    public function getUser(): ?User
    {
        return $this->user;
    }

    public function setUser(?User $user): self
    {
        $this->user = $user;

        return $this;
    }
}
