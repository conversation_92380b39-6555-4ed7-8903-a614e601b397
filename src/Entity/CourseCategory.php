<?php

declare(strict_types=1);

namespace App\Entity;

use App\Repository\CourseCategoryRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use Knp\DoctrineBehaviors\Contract\Entity\TranslatableInterface;
use Knp\DoctrineBehaviors\Model\Translatable\TranslatableTrait;
use Symfony\Component\Serializer\Annotation\Groups;

/**
 * @ORM\Entity(repositoryClass=CourseCategoryRepository::class)
 */
class CourseCategory implements TranslatableInterface
{
    use TranslatableTrait;

    public const string ORDER_TYPE_AUTO = 'auto';
    public const string ORDER_TYPE_MANUAL = 'manual';

    /**
     * @ORM\Id
     *
     * @ORM\GeneratedValue
     *
     * @ORM\Column(type="integer")
     *
     * @Groups({"list", "detail", "update-course", "course_section:index", "course_section:detail"})
     */
    private ?int $id;

    /**
     * @ORM\Column(type="string", length=255)
     *
     * @Groups({"list", "detail", "update-course", "course_section:index", "course_section:detail"})
     */
    private ?string $name;

    /**
     * @ORM\OneToMany(targetEntity=Course::class, mappedBy="category")
     *
     * @ORM\JoinColumn(nullable=true)
     */
    private $courses;

    /**
     * @ORM\Column(type="integer")
     *
     * @Groups({"course_section:index", "course_section:detail"})
     */
    private int $sort = 0;

    /**
     * @ORM\ManyToOne(targetEntity=CourseCategory::class, inversedBy="children")
     */
    private ?CourseCategory $parent = null;

    /**
     * @ORM\OneToMany(targetEntity=CourseCategory::class, mappedBy="parent")
     */
    private $children;

    /**
     * @ORM\ManyToMany(targetEntity=CourseSection::class, mappedBy="categories")
     */
    private $courseSections;

    /**
     * @ORM\ManyToMany(targetEntity=TypeCourse::class, inversedBy="courseCategories")
     */
    private $typeCourse;

    /**
     * @ORM\Column(type="string", length=20, nullable=true)
     */
    private ?string $orderType = self::ORDER_TYPE_AUTO;

    /**
     * @ORM\Column(type="json", nullable=true)
     */
    private ?array $orderProperties = [];

    /**
     * @ORM\Column(type="text", nullable=true)
     */
    private ?string $description = null;

    /**
     * @ORM\Column(type="boolean")
     */
    private bool $active = true;

    public function __construct()
    {
        $this->courses = new ArrayCollection();
        $this->children = new ArrayCollection();
        $this->courseSections = new ArrayCollection();
        $this->typeCourse = new ArrayCollection();
    }

    public function __toString()
    {
        return $this->getHierarchicalName();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId($id): self
    {
        $this->id = $id;

        return $this;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(string $name): self
    {
        $this->name = $name;

        return $this;
    }

    /**
     * @return Collection|Course[]
     */
    public function getCourses(): Collection
    {
        return $this->courses;
    }

    public function addCourse(Course $course): self
    {
        if (!$this->courses->contains($course)) {
            $this->courses[] = $course;
            $course->setCategory($this);
        }

        return $this;
    }

    public function removeCourse(Course $course): self
    {
        if ($this->courses->removeElement($course)) {
            // set the owning side to null (unless already changed)
            if ($course->getCategory() === $this) {
                $course->setCategory(null);
            }
        }

        return $this;
    }

    public function getSort(): ?int
    {
        return $this->sort;
    }

    public function setSort(int $sort): self
    {
        $this->sort = $sort;

        return $this;
    }

    public function getParent(): ?self
    {
        return $this->parent;
    }

    public function setParent(?self $parent): self
    {
        $this->parent = $parent;

        return $this;
    }

    /**
     * @return Collection|self[]
     */
    public function getChildren(): Collection
    {
        return $this->children;
    }

    public function addChild(self $child): self
    {
        if (!$this->children->contains($child)) {
            $this->children[] = $child;
            $child->setParent($this);
        }

        return $this;
    }

    public function removeChild(self $child): self
    {
        if ($this->children->removeElement($child)) {
            // set the owning side to null (unless already changed)
            if ($child->getParent() === $this) {
                $child->setParent(null);
            }
        }

        return $this;
    }

    public function getHierarchicalName()
    {
        $names = [];

        $parent = $this->getParent();
        while ($parent) {
            $names[] = $parent->getName();
            $parent = $parent->getParent();
        }

        $names = array_reverse($names);

        $names[] = $this->getName();

        return implode(' - ', $names);
    }

    /**
     * @return Collection<int, CourseSection>
     */
    public function getCourseSections(): Collection
    {
        return $this->courseSections;
    }

    public function addCourseSection(CourseSection $courseSection): self
    {
        if (!$this->courseSections->contains($courseSection)) {
            $this->courseSections[] = $courseSection;
            $courseSection->addCategory($this);
        }

        return $this;
    }

    public function removeCourseSection(CourseSection $courseSection): self
    {
        if ($this->courseSections->removeElement($courseSection)) {
            $courseSection->removeCategory($this);
        }

        return $this;
    }

    public function setTypeCourse($typeCourses): void
    {
        foreach ($this->getTypeCourse() as $old) {
            if (!\in_array($old, $typeCourses)) {
                $this->removeTypeCourse($old);
            }
        }

        foreach ($typeCourses as $t) {
            $this->addTypeCourse($t);
        }
    }

    /**
     * @return Collection<int, TypeCourse>
     */
    public function getTypeCourse(): Collection
    {
        return $this->typeCourse;
    }

    public function addTypeCourse(TypeCourse $typeCourse): self
    {
        if (!$this->typeCourse->contains($typeCourse)) {
            $this->typeCourse[] = $typeCourse;
        }

        return $this;
    }

    public function removeTypeCourse(TypeCourse $typeCourse): self
    {
        $this->typeCourse->removeElement($typeCourse);

        return $this;
    }

    public function getOrderType(): ?string
    {
        return $this->orderType;
    }

    public function setOrderType(?string $orderType): self
    {
        $this->orderType = $orderType;

        return $this;
    }

    public function getOrderProperties(): ?array
    {
        if (empty($this->orderProperties)) {
            return [
                'orderCriteria' => self::ORDER_CRITERIA_CREATED_DATE,
                'showNewAtStart' => false,
            ];
        }

        return $this->orderProperties;
    }

    public function setOrderProperties(?array $orderProperties): self
    {
        $this->orderProperties = $orderProperties;

        return $this;
    }

    public function isShowNewCoursesAtStart(): bool
    {
        return $this->orderProperties['showNewAtStart'] ?? false;
    }

    public const string ORDER_CRITERIA_CREATED_DATE = 'createdAt';
    public const string ORDER_CRITERIA_ALPHABETIC = 'alphabetic';

    public function orderCriteria(): string
    {
        return $this->orderProperties['orderCriteria'] ?? self::ORDER_CRITERIA_CREATED_DATE;
    }

    public function getDescription(): ?string
    {
        return $this->description;
    }

    public function setDescription(?string $description): self
    {
        $this->description = $description;

        return $this;
    }

    public function isActive(): ?bool
    {
        return $this->active;
    }

    public function setActive(bool $active): self
    {
        $this->active = $active;

        return $this;
    }

    public function getNormalize($text): string
    {
        $text = preg_replace('/\s+/', '-', $text);
        $text = preg_replace('/[^A-Za-z0-9&\-]/', '', $text);

        return strtolower($text);
    }
}
