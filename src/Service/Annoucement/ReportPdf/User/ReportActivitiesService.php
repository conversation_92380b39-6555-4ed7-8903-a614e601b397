<?php

namespace App\Service\Annoucement\ReportPdf\User;

use App\Entity\Announcement;
use App\Entity\AnnouncementUser;
use App\Entity\HistoryDeliveryTask;
use App\Entity\TaskCourse;
use App\Entity\TaskUser;
use App\Entity\User;


class ReportActivitiesService extends ReportBaseService
{
    public function generatePdfActivities(AnnouncementUser $announcementUser, $fullPath = null)
    {
        $taskUser = $this->fetchTaskAnnouncement($announcementUser->getUser(), $announcementUser->getAnnouncement());
        $data = array_merge(
            $this->headReportUser($announcementUser),
            [
                'taskUser'   => $taskUser,
            ]
        );

        $mPdf = $this->generate(
            'fundae/report_pdf_by_user/activities.html.twig',
            $data,

        );

        if (!empty($fullPath)) $mPdf->Output($fullPath, 'F');

        return $mPdf;
    }

    public function getHeadReportUser($announcementUser, User $user = null){
        return $this->headReportUser($announcementUser, $user);
    }
    

    public function fetchTaskAnnouncement(User $user, Announcement $announcement): array
    {
        $tasks = $this->taskUserService->getTaskAnnouncementGroup($announcement, $user);       
        $taskAll = [];
        foreach ($tasks as $task) {
            $taskUser = $this->getUserTasks($task, $user);
            $historyTask = $this->getHistoryTask($taskUser);

            $taskAll[] = [
                'title'       => $task->getTitle(),
                'description' => $task->getDescription(),
                'state'       => $historyTask ? intval($historyTask->getState()) : 0,
                'updatedAt'   => $historyTask ? $historyTask->getUpdatedAt() : ''
            ];
        }

        return $taskAll;
    }

    private function getUserTasks(TaskCourse $task, User $user): ?TaskUser
    {
        return $this->em->getRepository(TaskUser::class)->findOneBy(['task' => $task, 'user' => $user]);
    }

    private function getHistoryTask(?TaskUser $taskUser): ?HistoryDeliveryTask
    {
        return $taskUser ? $this->em->getRepository(HistoryDeliveryTask::class)->findOneBy(['taskUser' => $taskUser]) : null;
    }
}
