<?php

declare(strict_types=1);

namespace App\Service\Annoucement\Admin;

use App\Entity\Announcement;
use App\Entity\AnnouncementGroup;
use App\Entity\AnnouncementGroupSession;
use App\Entity\AnnouncementModality;
use App\Entity\AnnouncementTutor;
use App\Entity\Course;
use App\Entity\CourseCategory;
use App\Entity\TypeCourse;
use App\Entity\TypeMoney;
use App\Entity\User;
use App\Enum\TypeCourse as EnumTypeCourse;
use App\Enum\TypeSession;
use App\Modules\Announcement\DTO\AnnouncementDTO;
use App\Service\Annoucement\Email\AnnouncementEmailService;
use App\Service\Announcement\AnnouncementExtraService;
use App\Service\Course\Common\UserCourseService;
use App\Service\SettingsService;
use Doctrine\ORM\EntityManagerInterface;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\Security\Core\Security;
use Symfony\Contracts\Translation\TranslatorInterface;

class ExternalAnnouncementImportService
{
    private EntityManagerInterface $em;

    private LoggerInterface $massImportLogger;
    private const DEFAULT_TYPE_COURSE = 'on_site';
    private const int DEFAULT_PERSON_PER_GROUP = 999;

    // Excel modality constants
    private const string MODALITY_PRESENCIAL = 'PRESENCIAL';
    private const string MODALITY_ONLINE = 'ONLINE';
    private const string MODALITY_MIXTA = 'MIXTA';
    private const string MODALITY_VIRTUAL = 'VIRTUAL';
    private const string MODALITY_AULA_VIRTUAL = 'AULA_VIRTUAL';

    public function __construct(
        EntityManagerInterface $em,
        LoggerInterface $massImportLogger,
    ) {
        $this->em = $em;
        $this->massImportLogger = $massImportLogger;
    }

    public function getOrCreateAnnouncement($codForm, $courseName, $startAt, $finishAt, $hours, $extra = [], $timezoneString = 'Europe/Madrid', ?string $modality = null, $flush = true)
    {
        $announcement = $this->em->getRepository(Announcement::class)->findOneBy(['code' => $codForm]);
        if ($announcement) {
            return $announcement;
        }

        $course = $this->getOrCreateCourse($courseName, $modality);
        $announcement = (new Announcement())
            ->setCode($codForm)
            ->setCourse($course)
            ->setTimezone($timezoneString)
            ->setStartAt($startAt)
            ->setFinishAt($finishAt)
            ->setTotalHours((float) $hours)
            ->setStatus(Announcement::STATUS_FINISHED) // Default to FINISHED for mass import
            ->setExtra($extra)
            ->setUsersPerGroup(self::DEFAULT_PERSON_PER_GROUP);

        $this->em->persist($announcement);

        if ($flush) {
            $this->em->flush();
        }

        return $announcement;
    }

    /**
     * Gets or creates an AnnouncementGroup for the given Announcement.
     * For mass import, we want to reuse the first group if it already exists.
     *
     * @param Announcement $announcement the Announcement entity the group belongs to
     * @param int          $cost         optional cost for the group
     *
     * @return AnnouncementGroup returns the existing or newly created group
     */
    public function getOrCreateAnnouncementGroup(Announcement $announcement, int $cost = 0, string $typeMoney = 'EUR', bool $flush = true): AnnouncementGroup
    {
        // First, check if there's already a group for this announcement
        $existingGroup = $this->em->getRepository(AnnouncementGroup::class)->findOneBy(
            ['announcement' => $announcement],
            ['groupNumber' => 'ASC'] // Get the first group
        );

        if ($existingGroup) {
            return $existingGroup;
        }

        $lastGroup = $this->em->getRepository(AnnouncementGroup::class)->findOneBy(
            ['announcement' => $announcement],
            ['groupNumber' => 'DESC']
        );

        $nextGroupNumber = ($lastGroup && $lastGroup->getGroupNumber()) ? ($lastGroup->getGroupNumber() + 1) : 1;
        $moneyCodeISO = $this->getTypeMoneyByCodeIso($typeMoney);

        $group = (new AnnouncementGroup())
            ->setCode($announcement->getId() . '-' . $nextGroupNumber)
            ->setNumSessions(1) // Improvement: parameterize session number
            ->setPlace(TypeSession::TYPE_PRESENTIAL)
            ->setAnnouncement($announcement)
            ->setCost((string) $cost)
            ->setTypeMoney($moneyCodeISO)
            ->setGroupNumber($nextGroupNumber);

        $this->em->persist($group);

        if ($flush) {
            $this->em->flush();
        }

        return $group;
    }

    public function getOrCreateAnnouncementGroupSession(AnnouncementGroup $group, $startAt, $finishAt, $timezoneString = 'Europe/Madrid', ?string $modalityName = null, bool $flush = true)
    {
        $session = $this->em->getRepository(AnnouncementGroupSession::class)->findOneBy(
            ['announcementGroup' => $group],
            ['id' => 'DESC']
        );

        if ($session) {
            return $session;
        }

        $session = (new AnnouncementGroupSession())
            ->setAnnouncementGroup($group)
            ->setStartAt($startAt)
            ->setFinishAt($finishAt)
            ->setSessionNumber(0)
            ->setTimezone($timezoneString)
            ->setCost('0')
            ->setType(TypeSession::TYPE_PRESENTIAL);

        // Set modality if provided - find by name (Presencial, Online, Mixta)
        if ($modalityName) {
            $modality = $this->em->getRepository(AnnouncementModality::class)->findOneBy([
                'name' => $modalityName,
                'isActive' => true,
            ]);
            if ($modality) {
                $session->setModality($modality);
            }
        }

        $this->em->persist($session);

        if ($flush) {
            $this->em->flush();
        }

        return $session;
    }

    public function getOrCreateAnnouncementTutor(Announcement $announcement, AnnouncementGroup $group, $tutorEmail = null, bool $flush = true)
    {
        // First check if the group already has a tutor
        $existingTutor = $group->getAnnouncementTutor();
        if ($existingTutor) {
            return $existingTutor;
        }

        // Also check in the database to avoid constraint violations
        $existingTutorInDb = $this->em->getRepository(AnnouncementTutor::class)->findOneBy([
            'announcementGroup' => $group,
        ]);
        if ($existingTutorInDb) {
            $group->setAnnouncementTutor($existingTutorInDb);

            return $existingTutorInDb;
        }

        $userTutor = null;
        if ($tutorEmail) {
            $userTutor = $this->em->getRepository(User::class)
                ->createQueryBuilder('u')
                ->select('u')
                ->andWhere('u.email = :email')
                ->andWhere('JSON_CONTAINS(u.roles, :role) = 1')
                ->setParameter('email', $tutorEmail)
                ->setParameter('role', '"' . User::ROLE_TUTOR . '"')
                ->getQuery()
                ->getOneOrNullResult();
        }

        if (!$userTutor) {
            $userTutor = $this->em->getRepository(User::class)->getDefaultExternUser();
        }

        try {
            $announcementTutor = (new AnnouncementTutor())
                ->setTutor($userTutor)
                ->setName($userTutor->getFullName())
                ->setEmail($userTutor->getEmail())
                ->setAnnouncement($announcement)
                ->setAnnouncementGroup($group)
                ->setFilename('INVALID_FILE');

            $group->setAnnouncementTutor($announcementTutor);

            $this->em->persist($announcementTutor);
            $this->em->persist($group);

            if ($flush) {
                $this->em->flush();
            }

            return $announcementTutor;
        } catch (\Exception $e) {
            // If there's a constraint violation, try to find the existing tutor again
            if (false !== strpos($e->getMessage(), 'Duplicate entry') || false !== strpos($e->getMessage(), 'UNIQ_FBF66A9F851953B4')) {
                $this->massImportLogger->warning('Mass import: Tutor constraint violation, finding existing tutor', [
                    'group_id' => $group->getId(),
                    'error' => $e->getMessage(),
                ]);

                // Clear the entity manager to avoid issues
                $this->em->clear();

                // Reload the group and find the existing tutor
                $reloadedGroup = $this->em->find(AnnouncementGroup::class, $group->getId());
                $existingTutorAfterError = $this->em->getRepository(AnnouncementTutor::class)->findOneBy([
                    'announcementGroup' => $reloadedGroup,
                ]);

                if ($existingTutorAfterError) {
                    return $existingTutorAfterError;
                }
            }

            // Re-throw the exception if it's not a constraint violation or we couldn't find the existing tutor
            throw $e;
        }
    }

    private function getOrCreateCourse($courseName, ?string $modality = null)
    {
        $course = $this->em->getRepository(Course::class)->findOneBy(['code' => $courseName]);
        if ($course) {
            return $course;
        }

        $courseCategory = $this->em->getRepository(CourseCategory::class)->findOneBy([], ['id' => 'ASC']);
        $typeCourse = $this->getTypeCourseByModality($modality);

        if (!$typeCourse) {
            $this->massImportLogger->error('Mass import: TypeCourse not found', [
                'requested_modality' => $modality,
                'fallback_code' => self::DEFAULT_TYPE_COURSE,
                'course_name' => $courseName,
            ]);
            // Fallback to default external type course
            $typeCourse = $this->em->getRepository(TypeCourse::class)->findOneBy([
                'code' => self::DEFAULT_TYPE_COURSE,
                'denomination' => EnumTypeCourse::EXTERN,
            ]);
        }

        if (!$typeCourse) {
            throw new \Exception('No external TypeCourse found');
        }

        $courseLocale = 'es';

        $course = (new Course())
            ->setCode($courseName)
            ->setName($courseName)
            ->setTypeCourse($typeCourse)
            ->setCategory($courseCategory)
            ->setLocale($courseLocale)
            ->setDescription('External course')
            ->setOpen(false)
            ->setIsNew(false);

        $this->em->persist($course);

        return $course;
    }

    /**
     * Processes the Announcement sheet in Excel to create associated Announcements, Groups, and Sessions.
     *
     * @param Worksheet $announcementsSheet the Excel sheet containing formation data
     * @param string    $timezoneString     default timezone for announcements
     *
     * @return array returns an array with processed announcement data
     */
    public function processAnnouncements($announcementsSheet, $timezoneString = 'Europe/Madrid'): array
    {
        $announcements = [];
        $lastAnnouncementsRow = $announcementsSheet->getHighestDataRow();
        $processedCount = 0;
        $errorCount = 0;
        $totalRows = $lastAnnouncementsRow - 1;
        $batchSize = 500;

        $this->massImportLogger->info('Mass import: Starting announcement processing', [
            'total_rows' => $totalRows,
            'timezone' => $timezoneString,
        ]);

        for ($row = 2; $row <= $lastAnnouncementsRow; ++$row) {
            $currentRow = $row - 1; // Adjust for 0-based counting

            // Optimized logging - reduced frequency and data
            if (0 === $currentRow % $batchSize && $currentRow > 0) {
                $progressPercent = round(($currentRow / $totalRows) * 100, 1);
                $memoryUsage = round(memory_get_usage(true) / 1024 / 1024, 1);

                $this->massImportLogger->info('Mass import: Announcements progress', [
                    'progress' => $progressPercent . '%',
                    'rows' => $currentRow . '/' . $totalRows,
                    'created' => $processedCount,
                    'errors' => $errorCount,
                    'memory_mb' => $memoryUsage,
                ]);
            }

            try {
                $announcementDTO = $this->parseFormationRowToDTO($announcementsSheet, $row, $timezoneString);

                $announcement = $this->getOrCreateAnnouncement(
                    codForm: $announcementDTO->codForm,
                    courseName: $announcementDTO->courseName,
                    startAt: $announcementDTO->startAt,
                    finishAt: $announcementDTO->finishAt,
                    hours: $announcementDTO->hours,
                    extra: $announcementDTO->extra,
                    timezoneString: $timezoneString,
                    modality: $announcementDTO->modality,
                    flush: false
                );

                $firstAnnouncementGroup = $this->getOrCreateAnnouncementGroup(
                    announcement: $announcement,
                    cost: $announcementDTO->cost,
                    typeMoney: 'MXN',
                    flush: false
                );

                $this->getOrCreateAnnouncementGroupSession(
                    group: $firstAnnouncementGroup,
                    startAt: $announcementDTO->startAt,
                    finishAt: $announcementDTO->finishAt,
                    timezoneString: $timezoneString,
                    modalityName: $announcementDTO->modality,
                    flush: false
                );

                $this->getOrCreateAnnouncementTutor(
                    announcement: $announcement,
                    group: $firstAnnouncementGroup,
                    tutorEmail: $announcementDTO->tutor,
                    flush: false
                );

                $announcements[$announcementDTO->codForm] = [
                    'group' => $firstAnnouncementGroup,
                    'isExternal' => $announcementDTO->isExternal,
                ];
                ++$processedCount;
            } catch (\Exception $e) {
                ++$errorCount;

                // Check if EntityManager is closed and handle it
                if (!$this->em->isOpen()) {
                    $this->massImportLogger->error('Mass import: EntityManager closed, skipping remaining rows', [
                        'row' => $row,
                        'original_error' => $e->getMessage(),
                        'remaining_rows' => $lastAnnouncementsRow - $row,
                    ]);

                    // Break the loop as we can't continue without a working EntityManager
                    break;
                }

                $this->massImportLogger->error('Mass import: Error processing announcement row', [
                    'row' => $row,
                    'error' => $e->getMessage(),
                    'error_type' => \get_class($e),
                ]);
                continue;
            }
        }

        $this->massImportLogger->info('Mass import: Announcement processing completed', [
            'total_processed' => $processedCount,
            'total_errors' => $errorCount,
        ]);

        return $announcements;
    }

    public function getDateTimeFromExcel($cell, $timezoneString = 'Europe/Madrid'): \DateTimeImmutable
    {
        $date = \PhpOffice\PhpSpreadsheet\Shared\Date::excelToDateTimeObject($cell->getValue());

        return new \DateTimeImmutable($date->format('Y-m-d H:i:s'), new \DateTimeZone($timezoneString));
    }

    /**
     * Rounds a numeric value, applicable for both hours and costs.
     *
     * @param mixed $value can represent hours or cost; returns 0 if not numeric
     *
     * @return int rounded value or 0 if invalid
     */
    public function getRoundedValue($value): int
    {
        return is_numeric($value) ? (int) round($value) : 0;
    }

    public function getTypeMoneyByCodeIso(string $codeIso)
    {
        $typeMoney = $this->em->getRepository(TypeMoney::class)->findOneBy(['codeIso' => $codeIso]);

        if (!$typeMoney) {
            $this->massImportLogger->warning('Mass import: TypeMoney not found, using default', [
                'requested_code_iso' => $codeIso,
                'fallback' => 'first available TypeMoney',
            ]);
            $typeMoney = $this->em->getRepository(TypeMoney::class)->findOneBy([], ['id' => 'ASC']);
        }
        // Removed verbose TypeMoney found logging

        return $typeMoney;
    }

    /**
     * Helper function to parse a row into a AnnouncementDTO.
     */
    private function parseFormationRowToDTO(Worksheet $sheet, int $row, string $timezoneString): AnnouncementDTO
    {
        $codForm = $sheet->getCell("B$row")->getValue();
        if (!$codForm) {
            throw new \Exception("Row $row: 'codForm' is missing.");
        }

        $courseName = $sheet->getCell("D$row")->getValue();
        $startAt = $this->getDateTimeFromExcel($sheet->getCell("H$row"), $timezoneString);
        $finishAt = $this->getDateTimeFromExcel($sheet->getCell("I$row"), $timezoneString);
        $hours = $this->getRoundedValue($sheet->getCell("J$row")->getValue());
        $cost = $this->getRoundedValue($sheet->getCell("P$row")->getValue()); // COSTE MONEDA LOCAL field
        $tutor = $this->sanitizeCellString($sheet->getCell("O$row")->getValue());
        $modality = $this->sanitizeCellString($sheet->getCell("K$row")->getValue()); // Column K: Modality

        // Read Interno/Externo from column L to determine if external
        $extIntValue = $this->sanitizeCellString($sheet->getCell("L$row")->getValue());
        $isExternal = 'EXTERNO' === strtoupper($extIntValue);

        // Use dynamic extra data system instead of hardcoded COUNTRY
        $countryValue = $this->sanitizeCellString($sheet->getCell("F$row")->getValue());
        $extra = [
            '1' => $countryValue, // ID 1 = COUNTRY field on iberostar
            '2' => null,           // ID 2 = REGION field (not provided in Excel) on iberostar
        ];

        return new AnnouncementDTO($codForm, $courseName, $startAt, $finishAt, $hours, $cost, $tutor, $extra, $modality, $isExternal);
    }

    /**
     * Map modality from Excel to TypeCourse based on external denomination.
     */
    private function getTypeCourseByModality(?string $modality): ?TypeCourse
    {
        if (!$modality) {
            return null;
        }

        // Map modality strings to TypeCourse codes (external types use same codes as internal)
        $modalityMap = [
            self::MODALITY_PRESENCIAL => TypeCourse::CODE_ONSITE,
            self::MODALITY_ONLINE => TypeCourse::CODE_ONLINE,
            self::MODALITY_MIXTA => TypeCourse::CODE_MIXED,
            self::MODALITY_VIRTUAL => TypeCourse::CODE_VIRTUAL_CLASSROOM,
            self::MODALITY_AULA_VIRTUAL => TypeCourse::CODE_VIRTUAL_CLASSROOM,
        ];

        $normalizedModality = strtoupper(trim($modality));
        $typeCourseCode = $modalityMap[$normalizedModality] ?? null;

        if (!$typeCourseCode) {
            $this->massImportLogger->warning('Mass import: Unknown modality', [
                'modality' => $modality,
                'normalized' => $normalizedModality,
                'available_modalities' => array_keys($modalityMap),
            ]);

            return null;
        }

        return $this->em->getRepository(TypeCourse::class)->findOneBy([
            'code' => $typeCourseCode,
            'denomination' => EnumTypeCourse::EXTERN,
        ]);
    }

    private function sanitizeCellString($value): ?string
    {
        if (!\is_string($value) || '' === trim($value)) {
            return null;
        }
        $value = iconv('UTF-8', 'ASCII//TRANSLIT', $value);
        $value = preg_replace('/[\'"-]/', '', $value);
        $value = strtoupper($value);

        return $value;
    }
}
