<?php

namespace App\Controller\Chat;

use FOS\RestBundle\Controller\Annotations as Rest;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

/**
 * @Rest\Route("/api/chat")
 */
class ApiChatController extends ChatController
{
    /**
     * @Rest\Post("/get-server")
     * @param Request $request
     * @return Response
     */
    public function getServer(Request $request): Response
    {
        return parent::getServer($request); // TODO: Change the autogenerated stub
    }

    /**
     * @Rest\Post("/channel/create")
     * @param Request $request
     * @return Response
     */
    public function newChannel(Request $request): Response
    {
        return parent::newChannel($request); // TODO: Change the autogenerated stub
    }

    /**
     * @Rest\Post("/message/send")
     * @param Request $request
     * @return Response
     */
    public function sendMessage(Request $request): Response
    {
        return parent::sendMessage($request); // TODO: Change the autogenerated stub
    }

    /**
     * @Rest\Post("/message/like")
     * @param Request $request
     * @return Response
     */
    public function setLikeMessage(Request $request): Response
    {
        return parent::setLikeMessage($request); // TODO: Change the autogenerated stub
    }

    /**
     * @Rest\Post("/message/report")
     * @param Request $request
     * @return Response
     */
    public function reportMessage(Request $request): Response
    {
        return parent::reportMessage($request);
    }

    /**
     * @Rest\Post("/channel/messages")
     * @param Request $request
     * @return Response
     */
    public function getMessages(Request $request): Response
    {
        return parent::getMessages($request); // TODO: Change the autogenerated stub
    }

    /**
     * @Rest\Post("/channels")
     * @param Request $request
     * @return Response
     */
    public function getChannels(Request $request): Response
    {
        return parent::getChannels($request);
    }

    /**
     * @Rest\Post("/channel")
     * @param Request $request
     * @return Response
     */
    public function getChannel(Request $request): Response
    {
        return parent::getChannel($request); // TODO: Change the autogenerated stub
    }

    /**
     * @Rest\Post("/channel/remove")
     * @param Request $request
     * @return Response
     */
    public function removeChannel(Request $request): Response
    {
        return parent::removeChannel($request); // TODO: Change the autogenerated stub
    }
}
