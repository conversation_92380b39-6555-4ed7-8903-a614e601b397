<?php

declare(strict_types=1);

namespace App\Controller\Security\Integrations;

use App\Admin\Traits\SerializerTrait;
use App\Entity\IntegrationGroup;
use App\Entity\IntegrationMapping;
use App\Repository\IntegrationGroupRepository;
use App\Repository\IntegrationMappingRepository;
use App\Security\Integrations\IntegrationMappingService;
use App\Security\Integrations\IntegrationService;
use Doctrine\ORM\EntityManagerInterface;
use FOS\RestBundle\Controller\Annotations as Rest;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

/**
 * @Route("/admin/api/v1/integrations/mapping")
 */
class IntegrationMappingController extends AbstractController
{
    use SerializerTrait;

    private EntityManagerInterface $em;
    private IntegrationService $integrationService;
    private IntegrationMappingService $integrationMappingService;

    public function __construct(EntityManagerInterface $em, IntegrationService $integrationService, IntegrationMappingService $integrationMappingService)
    {
        $this->em = $em;
        $this->integrationService = $integrationService;
        $this->integrationMappingService = $integrationMappingService;
    }

    /**
     * @Rest\Get("/groups", name="admin_api_integrations_get_groups")
     */
    public function getGroups(IntegrationGroupRepository $integrationGroupRepository): Response
    {
        $groups = [];

        foreach ($integrationGroupRepository->findAll() as $group) {
            $groups[] = $this->getGroupAsArray($group);
        }

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => $groups,
        ]);
    }

    /**
     * @Rest\Post("/group", name="api_admin_integrations_save_group")
     */
    public function saveMappingGroup(Request $request, IntegrationGroupRepository $integrationGroupRepository, IntegrationMappingRepository $integrationMappingRepository): Response
    {
        $content = json_decode($request->getContent(), true);

        $integrationGroup = $integrationGroupRepository->findOneBy(['id' => $content['id']]);
        if (!$integrationGroup) {
            $integrationGroup = new IntegrationGroup();
        }
        $integrationGroup->setName($content['name'] ?? '')
            ->setActive($content['active'] ?? false);

        $mappings = [];

        $mappingData = $content['mappings'] ?? [];
        foreach ($mappingData as $md) {
            $mapping = null;

            if (!empty($md['id'])) {
                $mapping = $integrationMappingRepository->find($md['id']);
            }
            if (!$mapping) {
                $mapping = new IntegrationMapping();
            }
            $mapping->setEntity($md['entity'] ?? null)
                ->setIdentifier($md['identifier'] ?? null)
                ->setType($md['type'] ?? 'ENTITY')
                ->setIntegrationGroup($integrationGroup)
            ;
            $values = [];
            foreach (($md['mapping'] ?? []) as $v) {
                if (IntegrationMapping::TYPE_FILTER === $mapping->getType()) {
                    $values[$v['remote']] = array_column($v['local'], 'id');
                } else {
                    $values[$v['remote']] = $v['local'];
                }
            }
            $mapping->setMapping($values);

            $mappings[] = $mapping;
        }

        $integrationGroup->setIntegrationMappings($mappings);

        $this->em->persist($integrationGroup);
        $this->em->flush();

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => $this->getGroupAsArray($integrationGroup),
        ]);
    }

    /**
     * @param int|IntegrationGroup $group
     */
    private function getGroupAsArray($group): array
    {
        if (!($group instanceof IntegrationGroup)) {
            $group = $this->em->getRepository(IntegrationGroup::class)->find($group);
            if (!$group) {
                throw new \RuntimeException('Group not found');
            }
        }

        $mps = [];
        /** @var IntegrationMapping $mp */
        foreach ($group->getIntegrationMappings() as $mp) {
            $mps[] = [
                'id' => $mp->getId(),
                'entity' => $mp->getEntity(),
                'identifier' => $mp->getIdentifier(),
                'type' => $mp->getType(), // FILTER|ENTITY
                'group' => $group->getId(),
                'mapping' => $this->integrationMappingService->toEditableMapping($mp->getMapping(), $mp->getType()),
            ];
        }

        return [
            'id' => $group->getId(),
            'name' => $group->getName(),
            'active' => $group->isActive(),
            'mappings' => $mps,
        ];
    }
}
