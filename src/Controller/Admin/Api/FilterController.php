<?php

declare(strict_types=1);

namespace App\Controller\Admin\Api;

use App\Admin\Traits\SerializerTrait;
use App\Entity\FilterCategory;
use App\Repository\FilterRepository;
use Doctrine\ORM\EntityManagerInterface;
use FOS\RestBundle\Controller\Annotations as Rest;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

/**
 * @Route("/admin/api/v1/filters")
 */
class FilterController extends AbstractController
{
    use SerializerTrait;

    private EntityManagerInterface $em;

    public function __construct(EntityManagerInterface $em)
    {
        $this->em = $em;
    }

    /**
     * @Rest\Get("")
     */
    public function getByCategories(FilterRepository $filterRepository): Response
    {
        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => $filterRepository->createQueryBuilder('f')
                ->select('f.id', 'f.name', 'f.code')
                ->getQuery()
                ->getResult(),
        ]);
    }

    /**
     * @Rest\Get("/by-categories")
     */
    public function getFiltersByCategories(): Response
    {
        $user = $this->getUser();
        $filterCategories = $this->em->getRepository(FilterCategory::class)
             ->findFiltersAndCategoriesForUser($user);
        $data = [];
        foreach ($filterCategories as $row) {
            $categoryId = $row['categoryId'];
            if (!isset($data[$categoryId])) {
                $data[$categoryId] = [
                    'id' => $categoryId,
                    'name' => $row['categoryName'],
                    'filters' => [],
                ];
            }
            $data[$categoryId]['filters'][] = [
                'id' => $row['filterId'],
                'name' => $row['filterName'],
            ];
        }
        $data = array_values($data);

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => $data,
        ]);
    }
}
