<?php

namespace App\Controller\Admin;

use App\Admin\Field\TranslationField;
use App\Entity\CourseSegment;
use App\Entity\CourseSegmentCategory;
use App\Service\SettingsService;
use Doctrine\ORM\EntityManagerInterface;
use EasyCorp\Bundle\EasyAdminBundle\Config\Action;
use EasyCorp\Bundle\EasyAdminBundle\Config\Actions;
use EasyCorp\Bundle\EasyAdminBundle\Config\Crud;
use EasyCorp\Bundle\EasyAdminBundle\Config\KeyValueStore;
use EasyCorp\Bundle\EasyAdminBundle\Controller\AbstractCrudController;
use EasyCorp\Bundle\EasyAdminBundle\Field\IdField;
use EasyCorp\Bundle\EasyAdminBundle\Field\TextField;
use EasyCorp\Bundle\EasyAdminBundle\Provider\AdminContextProvider;
use EasyCorp\Bundle\EasyAdminBundle\Router\AdminUrlGenerator;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Contracts\Translation\TranslatorInterface;

class CourseSegmentCategoryCrudController extends AbstractCrudController
{
    private $em;
    private $requestStack;
    private $logger;
    protected $translator;
    private $context;
    private $settings;


    public function __construct(EntityManagerInterface $em, RequestStack $requestStack, LoggerInterface $logger, AdminContextProvider $context, TranslatorInterface $translator, SettingsService $settings)
    {
        $this->em =             $em;
        $this->requestStack =   $requestStack;
        $this->logger =         $logger;
        $this->context = $context;
        $this->translator = $translator;
        $this->settings = $settings;
    }
    public static function getEntityFqcn(): string
    {
        return CourseSegmentCategory::class;
    }

    public function configureCrud(Crud $crud): Crud
    {
        return $crud
            ->setEntityLabelInSingular($this->translator->trans('segment_category.label_in_singular', [], 'messages', $this->getUser()->getLocale()))
            ->setEntityLabelInPlural($this->translator->trans('segment_category.label_in_plural', [], 'messages', $this->getUser()->getLocale()))
            ->setSearchFields(['name'])
            ->addFormTheme('@A2lixTranslationForm/bootstrap_4_layout.html.twig')
             ->overrideTemplate('crud/detail', 'admin/course_segment/detail.html.twig');

    }

    public function configureActions (Actions $actions): Actions
    {
        return $actions
        ->add(Crud::PAGE_INDEX, Action::DETAIL);
    }

    public function configureFields(string $pageName): iterable
    {
        $entityId = $this->requestStack->getCurrentRequest()->get('entityId');

        $id   = IdField::new('id', '#')
            ->setTemplatePath('bundles/EasyAdminBundle/field-linked.html.twig');
        $name = TextField::new('name', $this->translator->trans('filter.configureFields.name', [], 'messages', $this->getUser()->getLocale()))
            ->setTemplatePath('bundles/EasyAdminBundle/field-linked.html.twig');


        $translations = TranslationField::new('translations', $this->translator->trans('help_category.configureFields.translations', [], 'messages', $this->getUser()->getLocale()), [
            'name' => [
                'label'      => $this->translator->trans('filter.configureFields.name', [], 'messages', $this->getUser()->getLocale()),
            ],
        ]);

        if (Crud::PAGE_INDEX === $pageName) {
            return [$id, $name];
        } elseif (Crud::PAGE_DETAIL === $pageName) {
            return [$id, $name];
        } elseif (Crud::PAGE_NEW === $pageName) {
            return [$name];
        } elseif (Crud::PAGE_EDIT === $pageName && $this->settings->get('app.multilingual') == true) {
            return [$name, $translations];
        } elseif (Crud::PAGE_EDIT === $pageName) {
            return [$name];
        }
    }

    public function configureResponseParameters(KeyValueStore $responseParameters): KeyValueStore
    {
        if (Crud::PAGE_DETAIL === $responseParameters->get('pageName')) {

            $courseSegmentCategoryRepository = $this->em->getRepository(CourseSegmentCategory::class);
            $entity = $this->context->getContext()->getEntity();
            $courseSegmentCategory = $courseSegmentCategoryRepository->find($entity->getPrimaryKeyValue());

            $courseSegmentRepository = $this->em->getRepository(CourseSegment::class);
            $courseSegment = $courseSegmentRepository->findBy([
                'courseSegmentCategory' => $courseSegmentCategory
            ]);

            $adminUrlGenerator = $this->get(AdminUrlGenerator::class);
            $referrer = $adminUrlGenerator
                ->unsetAll()
                ->setController(CourseSegmentCategoryCrudController::class)
                ->setAction('detail')
                ->setEntityId($courseSegmentCategory->getId())
                ->generateUrl();


            $responseParameters->set('courseSegmentCategory', $courseSegmentCategory);
            $responseParameters->set('courseSegment', $courseSegment);
            $responseParameters->set('referrer', $referrer);

    }
        return $responseParameters;
    }

}
