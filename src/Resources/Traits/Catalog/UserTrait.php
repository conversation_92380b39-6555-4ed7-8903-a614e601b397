<?php

namespace App\Resources\Traits\Catalog;

use App\Entity\User;
use App\Resources\DataFixtureBase\General\UserData;
use Symfony\Component\PasswordHasher\Hasher\UserPasswordHasherInterface;
use Doctrine\ORM\EntityManagerInterface;

trait UserTrait
{
    private UserPasswordHasherInterface $passwordHasher;
    private EntityManagerInterface $manager;

    public function __construct(
        UserPasswordHasherInterface $passwordHasher,
        EntityManagerInterface $manager
    ) {
        $this->passwordHasher = $passwordHasher;
        $this->manager = $manager;
    }

    public function saveUserData()
     {
         try {
             foreach (UserData::DEFAULT_DATA as $user) {

                 $userEntity = $this->manager->getRepository(User::class)->findOneBy(['email' => $user['email']]);

                 if (!$userEntity) {
                     $userEntity = new User();
                 }

                 // Always hash the password, whether it's a new user or existing user
                 $hashedPassword = $this->passwordHasher->hashPassword($userEntity, $user['password']);
                 $userEntity->setPassword($hashedPassword);

                 $userEntity->setFirstName($user['firstName'])
                     ->setLastName($user['lastName'])
                     ->setRoles($user['roles'])
                     ->setEmail($user['email'])
                     ->setIsActive($user['active'])
                     ->setOpen($user['open'])
                     ->setLocale($user['locale'])
                     ->setValidated($user['validated'])
                     ->setMeta($user['meta'])
                     ->setStarteam($user['starteam'])
                     ->setCustomFilters($user['custom_filters'])
                     ->setRemoteRoles($user['remote_roles']);

                 $this->manager->persist($userEntity);
             }
             $this->manager->flush();

         }catch (\Exception $e){
             throw new \Exception('Error saving UserFixtures: ' . $e->getMessage());
         }
     }
}