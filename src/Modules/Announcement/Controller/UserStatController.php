<?php

declare(strict_types=1);

namespace App\Modules\Announcement\Controller;

use App\Admin\Traits\SerializerTrait;
use App\Entity\User;
use App\Service\StatsUser\StatsUserService;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

/**
 * @Route("/admin/")
 */
class UserStatController extends AbstractController
{
    use SerializerTrait;

    private $statsUserService;

    private const PRIORITY_SECTIONS = ['itineraries', 'coursesFilters', 'voluntaryTraining'];

    private const ICON_MAPPING = [
        'itineraries' => 'fa fa-layer-group',
        'coursesFilters' => 'fa fa-tags',
        'voluntaryTraining' => '<i class="fa-solid fa-handshake-angle"></i>',
    ];

    public function __construct(
        StatsUserService $statsUserService
    ) {
        $this->statsUserService = $statsUserService;
    }

    private function handleException(\Exception $e): Response
    {
        $errorMessage = \sprintf('Error on line %d of %s: %s', $e->getLine(), $e->getFile(), $e->getMessage());

        return $this->sendResponse(['error' => true, 'status' => 500, 'message' => $errorMessage]);
    }

    /**
     * @Route("user/detail-stats/{id}", name="app_user_stat", methods={"GET"})
     */
    public function getVoluntaryTraining(User $user): Response
    {
        $error = false;
        $message = '';
        $code = 200;

        $courses = [
            'itineraries' => [],
            'announcements' => [],
            'coursesFilters' => [],
            'voluntaryTraining' => [],
        ];

        try {
            $courses['itineraries'] = $this->statsUserService->getItinerariesUser($user);
            $courses['coursesFilters'] = $this->statsUserService->getCoursesByFilter($user);
            $courses['voluntaryTraining'] = $this->statsUserService->getVoluntaryTrainingUser($user);
            $courses['announcements'] = $this->statsUserService->getAnnouncementUser($user);
        } catch (\Exception $e) {
            return $this->handleException($e);
        }

        $processedCourses = $this->processCourses($courses);

        $response = [
            'status' => $code,
            'error' => $error,
            'data' => Response::HTTP_OK === $code ? $processedCourses : $message,
        ];

        return $this->sendResponse($response, ['groups' => ['list']]);
    }

    /**
     * Processes the course sections to eliminate duplicates based on 'id' and adjust 'timeSpent' and 'icon'.
     *
     * @param array $courses array containing the course sections
     *
     * @return array modified array with duplicates handled
     */
    private function processCourses(array $courses): array
    {
        $seenCourseIds = [];

        foreach (self::PRIORITY_SECTIONS as $section) {
            if (isset($courses[$section]) && \is_array($courses[$section])) {
                foreach ($courses[$section] as &$course) {
                    if (isset($course['id'])) {
                        $courseId = $course['id'];
                        if (isset($seenCourseIds[$courseId])) {
                            $course['timeSpent'] = '0:00:00';

                            $firstSection = $seenCourseIds[$courseId];
                            if (isset(self::ICON_MAPPING[$firstSection])) {
                                $course['icon'] = self::ICON_MAPPING[$firstSection];
                            }
                        } else {
                            $seenCourseIds[$courseId] = $section;
                        }
                    }
                }
                unset($course);
            }
        }
        return $courses;
    }
}
