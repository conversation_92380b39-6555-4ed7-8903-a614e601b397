<?php

namespace App\Modules\User\Company\Controller;

use App\Entity\User;
use App\Service\SettingsService;
use App\Entity\UserCompany;

use App\Repository\UserCompanyRepository;

use App\Modules\Common\Controller\BaseVueController;
use App\Modules\User\Company\Services\UserCompanyCrudService;

use EasyCorp\Bundle\EasyAdminBundle\Config\Crud;
use Symfony\Contracts\Translation\TranslatorInterface;
use EasyCorp\Bundle\EasyAdminBundle\Config\KeyValueStore;
use Lexik\Bundle\JWTAuthenticationBundle\Services\JWTManager;
use Doctrine\ORM\EntityManagerInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\RequestStack;
use EasyCorp\Bundle\EasyAdminBundle\Provider\AdminContextProvider;
use Symfony\Component\Routing\Annotation\Route;
use FOS\RestBundle\Controller\Annotations as Rest;
use Symfony\Component\Security\Core\Security;



/**
 * @Route("/admin/")
 */
class UserCompanyCrudController extends BaseVueController 
{
    private JWTManager $JWTManager;
    private AdminContextProvider $context;
    private UserCompanyCrudService $userCompanyCrudService;
    private Security $security;

    protected function getUser()
    {
        return $this->security->getUser();
    }

    public function __construct(
        AdminContextProvider $context,
        TranslatorInterface $translator,
        JWTManager $JWTManager,
        SettingsService $settings,
        EntityManagerInterface $em,
        LoggerInterface $logger,
        RequestStack $requestStack,
        UserCompanyCrudService $userCompanyCrudService,
        Security $security
    )
    {
        parent::__construct($settings, $em, $logger, $requestStack, $translator);
        
        $this->context = $context;
        $this->JWTManager = $JWTManager;
        $this->userCompanyCrudService = $userCompanyCrudService;
        $this->security = $security;
    }

    public static function getEntityFqcn(): string
    {
        return UserCompany::class;
    }

    public function configureCrud(Crud $crud): Crud
    {
        return $crud->overrideTemplate('crud/index', 'admin/company/app.html.twig');
    }

    public function configureResponseParameters(KeyValueStore $responseParameters): KeyValueStore
    {
        if (Crud::PAGE_INDEX ===  $responseParameters->get('pageName')) {
            $this->configureAppResponseParameters(
                $responseParameters,
                $this->settings,
                $this->context,
                $this->JWTManager,
                [],
            );
        }
        return $responseParameters;
    }

    /**
     * @Rest\Get("company/all")
     * @return Response
     */
    public function getAllCompany(): Response
    {
        return $this->executeSafe(function () {
            $userCompanies = $this->em->getRepository(UserCompany::class)->findBy(['active' => 1]);
            $data = [];

            foreach ($userCompanies as $userCompany) {
                $data[] = $this->userCompanyCrudService->formatUserCompanyStructure($userCompany);
            }

            return $data;
        });
    }

    /**
     * @Rest\Post("company/create")
     * @param Request $request
     * @return Response
     */
    public function createCompay(Request $request)
    {
        return $this->executeSafe(function () {
            $request = $this->requestStack->getCurrentRequest();
            $content = json_decode($request->getContent(), true);
            $userCompany = new UserCompany();

            if (($result = $this->userCompanyCrudService->setUserCompanyData($userCompany, $content)) instanceof Response) return $result;

            return $this->userCompanyCrudService->formatUserCompanyStructure($userCompany);
        }, [], [], Response::HTTP_CREATED);
    }

    /**
     * Handle userCompany information
     * @Rest\Post("company/update")
     * @param Request $request
     * @param UserCompanyRepository $userCompanyRepository
     * @return Response
     */
    public function updateCompany(UserCompanyRepository $userCompanyRepository): Response
    {
        return $this->executeSafe(function () use ($userCompanyRepository) {
            $request = $this->requestStack->getCurrentRequest();
            $content = json_decode($request->getContent(), true);
            $id = $content['id'] ?? -1;
            $userCompany = $userCompanyRepository->find($id);

            if (!$userCompany) return $this->sendResponse([
                'status' => Response::HTTP_ACCEPTED,
                'error' => true,
                'data' => 'NOT_FOUND'
            ]);

            if (($result = $this->userCompanyCrudService->setUserCompanyData($userCompany, $content)) instanceof Response) return $result;

            return $this->userCompanyCrudService->formatUserCompanyStructure($userCompany);
        }, [], [], Response::HTTP_OK);
    }

    /**
     * Handle company information
     * @Rest\Put("company/{id}/state")
     */
    public function changeCompanyStateStatus(UserCompany $userCompany)
    {
        return $this->executeSafe(function () use ($userCompany) {
            $userCompany->setState(!$userCompany->getState());
            $this->em->persist($userCompany);
            $this->em->flush();    

            return $this->userCompanyCrudService->formatUserCompanyStructure($userCompany);
        }, [], [], Response::HTTP_OK);   
    }

    /**
     * Handle company information
     * @Rest\Post("company/{id}/delete")
     */
    public function deleteCompany(UserCompany $userCompany)
    {
        return $this->executeSafe(function () use ($userCompany) {
            $user = $this->em->getRepository(User::class)->findOneBy(['email' => $this->getUser()->getUsername()]);
            $userCompany->setActive(false);
            $userCompany->setDeletedAt(new \DateTimeImmutable());
            $userCompany->setDeletedBy($user);
            $this->em->persist($userCompany);
            $this->em->flush();

            return $this->userCompanyCrudService->formatUserCompanyStructure($userCompany);
        }, [], [], Response::HTTP_OK);
    }

}
