<?php

declare(strict_types=1);

namespace App\Repository;

use App\Entity\TypeDiploma;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<TypeDiploma>
 *
 * @method TypeDiploma|null find($id, $lockMode = null, $lockVersion = null)
 * @method TypeDiploma|null findOneBy(array $criteria, array $orderBy = null)
 * @method TypeDiploma[]    findAll()
 * @method TypeDiploma[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class TypeDiplomaRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, TypeDiploma::class);
    }

    public function add(TypeDiploma $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(TypeDiploma $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function getActiveAndApplyToCurseAndAll()
    {
        return $this->createQueryBuilder('t')
            ->select('t')
            ->where('t.active = 1')
            ->andWhere('t.applyTo in (1,2)')
            ->getQuery()
            ->getResult();
    }
}
