<?php

namespace App\Repository;

use App\Entity\Announcement;
use App\Entity\AnnouncementGroup;
use App\Entity\AnnouncementTutor;
use App\Entity\AnnouncementUser;
use App\Entity\FilesManager;
use App\Entity\User;
use App\Entity\UserFieldsFundae;
use App\Entity\ZipFileTask;
use App\Enum\TypeCourse as TypeCourseEnum;
use App\Service\Annoucement\Report\AnnouncementReportService;
use App\Service\FilesManager\FilesManagerService;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\ORM\Query\Expr\Join;
use Doctrine\Persistence\ManagerRegistry;
use Proxies\__CG__\App\Entity\AnnouncementTutor as EntityAnnouncementTutor;
use Symfony\Component\HttpFoundation\File\UploadedFile;

/**
 * @extends ServiceEntityRepository<AnnouncementGroup>
 *
 * @method AnnouncementGroup|null find($id, $lockMode = null, $lockVersion = null)
 * @method AnnouncementGroup|null findOneBy(array $criteria, array $orderBy = null)
 * @method AnnouncementGroup[]    findAll()
 * @method AnnouncementGroup[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class AnnouncementGroupRepository extends ServiceEntityRepository
{
    private FilesManagerService $filesManagerService;

    public function __construct(ManagerRegistry $registry, FilesManagerService $filesManagerService)
    {
        parent::__construct($registry, AnnouncementGroup::class);
        $this->filesManagerService = $filesManagerService;
    }

    public function add(AnnouncementGroup $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(AnnouncementGroup $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    /**
     * @param AnnouncementGroup $group
     * @param array $tutorData
     * @param UploadedFile|array|null $cv
     * @return array
     * @throws \Exception
     */
    public function setTutorInformation(AnnouncementGroup $group, array $tutorData, $cv = null): array
    {
        $errors = [];
        if (empty($tutorData['tutorId'])) $errors[] = 'Tutor ID is required';
        if (empty($tutorData['dni'])) $errors[] =  'Tutor DNI is required';
        if (empty($tutorData['email'])) $errors[] = 'Tutor Email is required';
        if (empty($tutorData['telephone'])) $errors[] = 'Tutor telephone is required';
        if (count($errors) > 0) {
            return [
                'error' => true,
                'data' => $errors
            ];
        }

        $tutor = $group->getAnnouncementTutor();
        $announcement = $group->getAnnouncement();
        if (!$tutor) {
            $tutor = new AnnouncementTutor();
            $tutor->setAnnouncement($announcement)
                ->setAnnouncementGroup($group);
        }

        $user = $this->_em->getRepository(User::class)->find($tutorData['tutorId']);
        // Update user fields fundae
        $userFieldsFundae = $user->getUserFieldsFundae();
        if (!$userFieldsFundae)
        {
            $userFieldsFundae = new UserFieldsFundae();
            $userFieldsFundae->setUser($user);
            $user->setUserFieldsFundae($userFieldsFundae);
        }

        $tutor->setTutor($user)
            ->setDni($tutorData['dni'])
            ->setEmail($tutorData['email'])
            ->setTelephone($tutorData['telephone'])
            ->setTutoringTime($tutorData['tutoringTime']);

        $userFieldsFundae->setDni($tutor->getDni())
            ->setEmailWork($tutor->getEmail())
            ->setTelephone($tutor->getTelephone());

        if ($cv instanceof UploadedFile) {
            // Uploading a new file
            $previousFileManager = $tutor->getCvFilesManager();
            if ($previousFileManager)
            {
                $this->_em->remove($previousFileManager);
                $tutor->setCvFilesManager(null);
            }
            $filesManager = $this->filesManagerService->upload(
                $cv,
                'announcement' . DIRECTORY_SEPARATOR . 'tutorCV',
                User::ROLE_TUTOR
            );
            $tutor->setCvFilesManager($filesManager);
            $tutor->setFilename($filesManager->getFilename());
            $cloneCv = $this->filesManagerService->clone($filesManager, 'users/cv');
            $userFieldsFundae->setCvFilesManager($cloneCv);
        } elseif (is_array($cv))
        {
            $currentCvFile = $tutor->getCvFilesManager();
            if (!$currentCvFile || $currentCvFile->getFilename() !== $cv['filename'])
            {
                // Data from other source, source: UserFieldsFundae
                if ($cv['source'] === FilesManager::TYPE_FILES_MANAGER)
                {
                    // Find the file
                    $srcFilesManager = $this->filesManagerService->getFilesManager($cv['filename']);
                    if (!$srcFilesManager) throw new \Exception('Source file not found');
                    $cloned = $this->filesManagerService->clone($srcFilesManager, 'announcement' . DIRECTORY_SEPARATOR . 'tutorCV');
                    $tutor->setCvFilesManager($cloned)
                        ->setFilename($cloned->getFilename())
                    ;
                }
            }
        }

        $this->_em->persist($tutor);
        $this->_em->persist($userFieldsFundae);
        $this->_em->flush();

        return [
            'error' => false,
            'data' => $this->_em->getRepository(AnnouncementTutor::class)->getTutorByGroup($group)
        ];
    }

    public function findAnnouncementAndTutor(Announcement $announcement)
    {
        return $this->createQueryBuilder('ag')
            ->select('ag.id, ag.companyProfile, ag.companyCif, ag.code,
            ag.fileNumber, 0 as tutor,
            0 as totalUsers
            ')
            ->andWhere('ag.announcement = :announcement')
            ->setParameter('announcement', $announcement)
            ->getQuery()
            ->getResult();
    }

    public function totalUserByAnnouncement(AnnouncementGroup $announcementGroup)
    {
        return $this->createQueryBuilder('ag')
            ->select('au.id')
            ->innerJoin(
                AnnouncementUser::class,
                'au',
                Join::WITH,
                'au.announcement = ag.announcement'
            )
            ->andWhere('ag.id = :announcementGroup')
            ->setParameter('announcementGroup', $announcementGroup)
            ->getQuery()
            ->getResult();
    }

    public function findTutoName(AnnouncementGroup $announcementGroup)
    {
        return $this->createQueryBuilder('ag')
            ->select('u.firstName, u.lastName')
            ->innerJoin(
                EntityAnnouncementTutor::class,
                'at',
                Join::WITH,
                'at.announcementGroup = ag.id'
            )
            ->innerJoin(
                User::class,
                'u',
                Join::WITH,
                'u.id = at.tutor'
            )
            ->andWhere('ag.id = :announcementGroup')
            ->setParameter('announcementGroup', $announcementGroup)
            ->getQuery()
            ->getResult();
    }


    public function findAnnouncementGroupUser($announcement, $user)
    {
        return $this->createQueryBuilder('ag')
            ->join('ag.announcementUsers', 'au')
            ->where('ag.announcement =:announcement')
            ->andWhere('au.user =:user')
            ->setParameters([
                'announcement' => $announcement,
                'user' => $user
            ])
            ->setMaxResults(1)
            ->getQuery()
            ->getOneOrNullResult();
    }

    public function tutorZipReportStatus($idGroup): array
    {
        $task = $this->_em->getRepository(ZipFileTask::class)->createQueryBuilder('t')
            ->where('t.entityId = :groupId AND t.type = :type AND t.createdBy IS NOT NULL')
            ->setParameter('groupId', $idGroup)
            ->setParameter('type', AnnouncementReportService::TYPE_GROUP)
            ->orderBy('t.finishedAt', 'DESC')
            ->setMaxResults(1)
            ->getQuery()
            ->getOneOrNullResult();


        $data = [];
        if ($task) {
            $data = [
                'status' => $task->getStatus(),
                'statusAsString' => $task->getStatusAsString(),
                'date' => ($date = $task->getFinishedAt()) ? $date->format('c') : null
            ];
        }
        $data['created'] = $task != null;

        return $data;
    }

    public function getGroupsByTutor($announcement, $tutor)
    {
        return $this->createQueryBuilder('ag')
            ->join('ag.announcementTutor', 'at')
            ->where('ag.announcement =:announcement')
            ->andWhere('at.tutor =:tutor')
            ->setParameters([
                'announcement' => $announcement,
                'tutor' => $tutor
            ])
            ->getQuery()
            ->getResult();
    }

    public function refreshGroupNumbers(Announcement $announcement) {
        $allGroups = $this->findBy(
            [
                'announcement' => $announcement
            ],
            [
                'id' => 'ASC'
            ]
        );

        $groupNumber = 1;
        foreach ($allGroups as $g) {
            $g->setGroupNumber($groupNumber);
            $groupNumber++;
            $this->_em->persist($g);
        }
        $this->_em->flush();
    }

    public function refreshAnnouncementExternGroups(Announcement $announcement)
    {
        $typeCourse = $announcement->getCourse()->getTypeCourse();
        if ($typeCourse->getDenomination() !== TypeCourseEnum::EXTERN) return;
        /** @var AnnouncementGroup|null $firstGroup */
        $firstGroup = $this->findOneBy([
            'announcement' => $announcement,
            'groupNumber' => 1
        ]);

        if (!$firstGroup) return;
        $firstGroupTutor = $firstGroup->getAnnouncementTutor();
        /** @var AnnouncementGroup[] $allOtherGroups */
        $allOtherGroups = $this->createQueryBuilder('g')
            ->select('g')
            ->where('g.announcement = :announcement')
            ->andWhere('g.groupNumber != 1')
            ->setParameter('announcement', $announcement)
            ->getQuery()
            ->getResult();
        if (empty($allOtherGroups)) return;

        foreach ($allOtherGroups as $group)
        {
            $group->setPlace($firstGroup->getPlace())
                ->setCost($firstGroup->getCost())
                ->setTypeMoney($firstGroup->getTypeMoney());

            $announcementTutor = $group->getAnnouncementTutor();
            if (!$announcementTutor) {
                $announcementTutor = new AnnouncementTutor();
                $announcementTutor->setAnnouncementGroup($group)
                    ->setAnnouncement($announcement)
                    ->setFilename($firstGroupTutor->getFilename())
                ;
            }

            $announcementTutor->setName($firstGroupTutor->getName())
                ->setEmail($firstGroupTutor->getEmail())
                ->setTutor($firstGroupTutor->getTutor())
            ;

            $this->_em->persist($group);
            $this->_em->persist($announcementTutor);
        }
        $this->_em->flush();
    }
}
