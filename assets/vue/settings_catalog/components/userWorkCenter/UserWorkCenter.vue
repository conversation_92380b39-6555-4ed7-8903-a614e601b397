<script>
import Spinner from "../../../admin/components/base/Spinner.vue";
import {get, sync} from "vuex-pathify";
import BaseSwitch from "../../../base/BaseSwitch.vue";
import JwPagination from 'jw-vue-pagination';
import Multiselect from "vue-multiselect";

const PAGE_SIZE = 10;
const customLabels = {
    first: '<<',
    last: '>>',
    previous: '<',
    next: '>'
};

export default {
  name: "UserWorkCenter",
  components: {BaseSwitch, Spinner, JwPagination, Multiselect},    
  data() {
    return {
      customLabels,
      defaultValue: '',
      userWorkCenter:[],
      allUserWorkCenter:[],
      btnFilter: true,
      btnClearFilter: false,
    };
  },
  computed: {
    loading: get('catalogModule/loading'), 
    catalogs: sync('catalogModule/catalogs'), 
  },
  async created() { 
    const userWorkCenter = await this.$store.dispatch('catalogModule/load', '/admin/userWorkCenter/all'); 
    console.log(userWorkCenter);
    this.userWorkCenter = this.allUserWorkCenter = userWorkCenter.data;
  },
  methods: {    
    changeActiveStatus(index) {
      const value = this.userWorkCenter[index];
      this.$store.dispatch('catalogModule/changeActiveState',
          {endpoint: `/admin/userWorkCenter/${value.id}/state`, data: {id: value.id, state: value.state}}).then(r => {

      });
    },
    filterActive(){
      this.btnFilter = !this.btnFilter;
    },
    clearfilter(){
      this.userWorkCenter = this.allUserWorkCenter;
      this.btnClearFilter = false;
      this.defaultValue= '';
    },
    selectFilter(){
      let filterSettings=[];

      this.allUserWorkCenter.forEach((item) => {
        if(item.name === this.defaultValue) filterSettings.push(item);      
      });

      this.userWorkCenter = filterSettings;     
      this.filterActive();
      this.btnClearFilter = true;
    },
    onChangePage(userWorkCenter) {
        this.userWorkCenter = userWorkCenter;
    },
  }
}
</script>

<template>
  <div>
    <div class="d-flex align-items-center justify-content-end">
      <!-- <div class="col-md-4 col-xs-12 mr-auto justify-content-end">
        <div v-if="btnClearFilter">
          <button class="btn btn-danger"  @click="clearfilter()"  style="margin: 0.5rem">
            <i class="fa fa-trash" >{{ $t('Clear filters') }}</i>
          </button>
        </div>
        <div v-if="btnFilter">
          <label>{{ defaultValue }}</label>
          <button class="btn btn-primary"  @click="filterActive()">
            <i class="fa fa-filter" >{{ $t('filters') }}</i>
          </button>
        </div>
        <div v-else>
          <label>{{ $t('UserWorkCenter') }}</label>
          <input type="text" class="form-control" v-model="defaultValue">
          <button class="btn btn-primary"  @click="selectFilter()">
            <i class="fa fa-filter" >{{ $t('filters') }}</i>
          </button>
        </div>
      </div> -->
      <router-link type="button" class="btn btn-primary"
                   :to="{name: 'UserWorkCenterCreate', params: this.$route.params}"
      >
        {{ $t('COMMON.CREATE') }}
      </router-link>
    </div>    
    <div class="col-12 d-flex flex-row align-items-center justify-content-center" v-if="loading">
      <spinner />
    </div>    

    <table class="table table-condensed mt-3" v-else>
      <thead>
      <tr>
        <th></th>
        <th>{{ $t('NAME') }}</th>
        <th>{{ $t('DESCRIPTION') }}</th>
        <th>{{ $t('STATE') }}</th>
        <th></th>
      </tr>
      </thead>
      <tbody>
      <tr v-for="(c, index) in userWorkCenter" :key="c.id">
        <td></td>
        <td>{{ c.name }}</td>
        <td>{{ c.description }}</td>
        <td>
          <BaseSwitch :tag="`switcher-UserWorkCenter-${c.id}`" v-model="c.state" @change="changeActiveStatus(index)" />
        </td>
        <td>
          <div class="dropdown">
            <button class="btn btn-default" type="button" :id="`dropdown-menu-${c.id}`" data-bs-toggle="dropdown" aria-expanded="false">
              <i class="fa fa-ellipsis-h"></i>
            </button>
            <ul class="dropdown-menu" :aria-labelledby="`dropdown-menu-${c.id}`">
              <li><router-link class="dropdown-item" :to="{ name: 'UserWorkCenterUpdate', params: {...$route.params, id: c.id} }">{{ $t('EDIT') }}</router-link></li>
            </ul>
          </div>
        </td>
      </tr>      
      <tr v-if="userWorkCenter.length>9">
        <td colspan="6">
          <div class="d-flex align-items-center justify-content-start"  >
            <jw-pagination 
              :items="userWorkCenter"
              @changePage="onChangePage"
              :labels="customLabels"
            ></jw-pagination>
          </div>
        </td>
      </tr>
      </tbody>
    </table>
  </div>
</template>

<style scoped lang="scss">

</style>
