openapi: 3.0.0
info:
  title: Easylearning Admin API v2
  description: API for Easylearning administration
  version: 2.0.0

servers:
  - url: /api/v2
    description: Campus server v2

security:
  - bearerAuth: []

paths:
  /courses/{courseId}/chapter/{chapterId}/lti-launch:
    get:
      tags:
        - Chapter
      summary: Get launch request of a lti chapter
      description: |
        Generate a request string to use against an external tool provider to execute a LTI resource
        linked to the current course and chapter.
      operationId: getLaunchLtiChapter
      parameters:
        - name: courseId
          in: path
          required: true
          schema:
            type: integer
            minimum: 1
        - name: chapterId
          in: path
          required: true
          schema:
            type: integer
            minimum: 1
      responses:
        200:
          description: Get Launch request against an external tool
          content:
            text/html:
              schema:
                type: string
        400:
          description: Course Id or Chapter Id is invalid
        404:
          description: Lti Chapter not found
        422:
          description: Unprocessable Entity
        5XX:
          description: Internal server error

  /locales:
    get:
      tags:
        - Locales
      summary: Get available locales
      description: Retrieves the list of available locales (languages) in the system
      operationId: getLocales
      responses:
        '200':
          description: List of available locales
          content:
            application/json:
              schema:
                type: array
                items:
                  type: string
                  example: "es"
                  description: "ISO language code"

  /purchases:
    get:
      tags:
        - Purchases
      summary: Get purchases list
      description: Retrieve a paginated list of purchases for the authenticated user with filtering and sorting options
      operationId: getPurchases
      parameters:
        - name: page
          in: query
          description: Page number for pagination (requires page_size)
          required: false
          schema:
            type: integer
            minimum: 1
        - name: page_size
          in: query
          description: Number of items per page (requires page)
          required: false
          schema:
            type: integer
            minimum: 1
        - name: amount_min
          in: query
          description: Minimum purchase amount in cents/centavos
          required: false
          schema:
            type: integer
            minimum: 0
        - name: amount_max
          in: query
          description: Maximum purchase amount in cents/centavos
          required: false
          schema:
            type: integer
            minimum: 0
        - name: status
          in: query
          description: Filter by purchase status
          required: false
          schema:
            type: string
            enum: [pending, completed, cancelled]
        - name: start_date
          in: query
          description: Filter purchases created after this date (format Y-m-d H:i:s)
          required: false
          schema:
            type: string
            format: date-time
        - name: end_date
          in: query
          description: Filter purchases created before this date (format Y-m-d H:i:s)
          required: false
          schema:
            type: string
            format: date-time
        - name: sort_by
          in: query
          description: Field to sort by (requires sort_dir)
          required: false
          schema:
            type: string
            enum: [id, status, created_at, amount]
        - name: sort_dir
          in: query
          description: Sort direction (requires sort_by)
          required: false
          schema:
            type: string
            enum: [asc, desc]
      responses:
        '200':
          description: List of purchases retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/Purchase'
                  metadata:
                    type: object
                    properties:
                      pagination:
                        type: object
                        properties:
                          total:
                            type: integer
                            description: Total number of items
                          count:
                            type: integer
                            description: Number of items in current page
                          per_page:
                            type: integer
                            description: Number of items per page
                          current_page:
                            type: integer
                            description: Current page number
                          total_pages:
                            type: integer
                            description: Total number of pages
        '400':
          description: Validation errors
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationError'
        '401':
          description: Unauthorized
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
              example:
                message: "Internal server error"
    post:
      tags:
        - Purchases
      summary: Create a purchase
      description: Create a new purchase for the current user
      operationId: postCreatePurchase
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                purchasable_item_id:
                  $ref: '#/components/schemas/Uuid'
      responses:
        '201':
          description: Purchase created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Purchase'
        '400':
          description: Validation errors
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationError'
        '401':
          description: Unauthorized
        '404':
          description: Purchasable item not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
              example:
                message: "Purchasable item not found"
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
              example:
                message: "Internal server error"

  /purchases/{purchaseId}:
    get:
      tags:
        - Purchases
      summary: Get purchase details
      description: Retrieve details of a specific purchase for the authenticated user
      operationId: getPurchase
      parameters:
        - name: purchaseId
          in: path
          required: true
          schema:
            $ref: '#/components/schemas/Uuid'
      responses:
        '200':
          description: Purchase details retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    $ref: '#/components/schemas/Purchase'
        '400':
          description: Invalid UUID format
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
              example:
                message: "Invalid UUID format"
        '401':
          description: Unauthorized
        '404':
          description: Purchase not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
              example:
                message: "Purchase not found"
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
              example:
                message: "Internal server error"

  /purchases/{purchaseId}/billing-data:
    patch:
      tags:
        - Purchases
      summary: Update purchase billing data
      description: |
        Updates the billing data for a specific purchase. The authenticated user must own the purchase or have admin privileges.

        **Features:**
        - Updates billing information for the purchase
        - Optionally saves the billing data as default for the user (save_as_default: true)
        - Validates all required billing fields
        - Supports metadata for additional custom fields

        **Access Control:**
        - Users can only update billing data for their own purchases
        - Admin users can update billing data for any purchase
      operationId: patchPurchaseBillingData
      parameters:
        - name: purchaseId
          in: path
          required: true
          schema:
            $ref: '#/components/schemas/Uuid'
          description: The unique identifier of the purchase
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - tin
                - first_name
                - last_name
                - address
                - postal_code
                - city
                - country
              properties:
                tin:
                  type: string
                  maxLength: 50
                  description: Tax identification number
                  example: "12345678A"
                first_name:
                  type: string
                  maxLength: 100
                  description: First name
                  example: "John"
                last_name:
                  type: string
                  maxLength: 100
                  description: Last name
                  example: "Doe"
                address:
                  type: string
                  maxLength: 255
                  description: Street address
                  example: "123 Main Street"
                postal_code:
                  type: string
                  maxLength: 20
                  description: Postal code
                  example: "12345"
                city:
                  type: string
                  maxLength: 100
                  description: City
                  example: "Madrid"
                country:
                  type: string
                  minLength: 2
                  maxLength: 3
                  pattern: "^[A-Z]{2,3}$"
                  description: Country code (ISO 2-3 letter format)
                  example: "ES"
                metadata:
                  type: object
                  additionalProperties:
                    type: string
                    maxLength: 500
                  maxProperties: 20
                  description: |
                    Additional metadata as key-value pairs.
                    - Maximum 20 keys allowed
                    - Each value must be a string with max 500 characters
                    - Total metadata size cannot exceed 2KB
                    - Allowed keys: company, department, reference, notes, invoice_reference, purchase_order, cost_center, project_code, billing_reference
                  example:
                    company: "ACME Corp"
                    department: "IT"
                save_as_default:
                  type: boolean
                  description: Whether to save this billing data as default for the user
                  example: false
            example:
              tin: "12345678A"
              first_name: "John"
              last_name: "Doe"
              address: "123 Main Street"
              postal_code: "12345"
              city: "Madrid"
              country: "ES"
              metadata:
                company: "ACME Corp"
              save_as_default: false
      responses:
        '200':
          description: Purchase billing data updated successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: "Purchase billing data updated successfully"
        '400':
          description: Invalid request data
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationError'
              examples:
                invalid_uuid:
                  summary: Invalid UUID format
                  value:
                    message: "Validation failed"
                    metadata:
                      violations:
                        - field: "purchaseId"
                          message: "Invalid UUID format"
                invalid_billing_data:
                  summary: Invalid billing data
                  value:
                    message: "Validation failed"
                    metadata:
                      violations:
                        - field: "tin"
                          message: "This field is required"
                        - field: "country"
                          message: "Country must be a valid ISO country code (2-3 uppercase letters)"
        '401':
          description: Unauthorized - Authentication required
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
              example:
                message: "Authentication required"
        '403':
          description: Forbidden - User cannot access this purchase
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
              example:
                message: "Access denied to this purchase"
        '404':
          description: Purchase not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
              example:
                message: "Purchase not found"
        '422':
          description: Unprocessable Entity - Invalid purchase ID format
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
              example:
                message: "Invalid UUID format for purchase ID"
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
              example:
                message: "Internal server error"

  /users/{userId}/billing-data:
    get:
      tags:
        - Billing Data
      summary: Get user billing data
      description: Retrieve billing data for a specific user. Only admins can access other users' data, regular users can only access their own data.
      operationId: getBillingData
      parameters:
        - name: userId
          in: path
          required: true
          schema:
            type: integer
            minimum: 1
          description: The ID of the user whose billing data to retrieve
      responses:
        '200':
          description: Billing data retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    $ref: '#/components/schemas/BillingData'
        '400':
          description: Invalid user ID
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationError'
              example:
                message: "Validation failed"
                metadata:
                  violations:
                    userId: "User ID must be a positive integer"
        '401':
          description: Unauthorized
        '403':
          description: Forbidden - User not allowed to access this data
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
              example:
                message: "User not allowed to access this data"
        '404':
          description: Billing data not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
              example:
                message: "Billing data not found"
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
              example:
                message: "Internal server error"

components:
  schemas:
    Uuid:
      type: string
      format: uuid
      example: "550e8400-e29b-41d4-a716-************"
      description: "UUID v4"
    Locale:
      type: object
      properties:
        code:
          type: string
          example: "es"
          description: "ISO language code"
    Purchase:
      type: object
      properties:
        id:
          $ref: "#/components/schemas/Uuid"
        user_id:
          type: integer
          format: int64
          description: User's unique ID
        status:
          type: string
          enum: [ pending, completed, cancelled ]
          description: Purchase status
        amount:
          type: integer
          description: Total amount of the purchase in cents/centavos
        currency:
          type: string
          description: Currency code
        tax_rate:
          type: number
          description: Tax rate applied to the purchase
        tax_amount:
          type: integer
          description: Tax amount in cents/centavos
        items:
          type: array
          items:
            $ref: "#/components/schemas/PurchaseItem"
    PurchaseItem:
      type: object
      properties:
        id:
          $ref: "#/components/schemas/Uuid"
        price_amount:
          type: integer
          description: Price amount in cents/centavos
        price_currency:
          type: string
          description: Price currency code
    BillingData:
      type: object
      properties:
        id:
          $ref: '#/components/schemas/Uuid'
        user_id:
          type: integer
          format: int64
          description: User's unique ID
        tin:
          type: string
          description: Tax identification number
          example: "12345678A"
        first_name:
          type: string
          description: First name
          example: "John"
        last_name:
          type: string
          description: Last name
          example: "Doe"
        address:
          type: string
          description: Street address
          example: "123 Main Street"
        postal_code:
          type: string
          description: Postal code
          example: "12345"
        city:
          type: string
          description: City
          example: "Madrid"
        country:
          type: string
          description: Country
          example: "Spain"
        metadata:
          type: object
          additionalProperties: true
          description: Additional metadata as key-value pairs
          example:
            company: "ACME Corp"
            department: "IT"
    Error:
      type: object
      properties:
        message:
          type: string
          description: Error message
    ValidationError:
      type: object
      properties:
        message:
          type: string
          description: Validation error message
        metadata:
          type: object
          properties:
            violations:
              type: object
              additionalProperties:
                type: string
              description: Field validation violations

  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
